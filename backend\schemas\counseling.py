"""
心理咨询分析相关的Pydantic模型
"""

from pydantic import BaseModel, Field
from typing import Optional, Dict, Any, List
from datetime import datetime
from enum import Enum


class AnalysisStatus(str, Enum):
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"


class RiskLevel(str, Enum):
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class RecordingStatus(str, Enum):
    UPLOADED = "uploaded"
    TRANSCRIBING = "transcribing"
    TRANSCRIBED = "transcribed"
    ANALYZING = "analyzing"
    COMPLETED = "completed"
    FAILED = "failed"


class CounselingSessionCreate(BaseModel):
    """创建心理咨询会话请求"""
    therapist_id: Optional[str] = None
    client_name: Optional[str] = None
    client_gender: Optional[str] = None
    client_age: Optional[int] = None
    title: Optional[str] = None
    description: Optional[str] = None
    session_date: Optional[datetime] = None
    duration_minutes: Optional[int] = None


class CounselingDialogueCreate(BaseModel):
    """创建心理咨询对话请求"""
    session_id: int
    dialogue_text: str


class CounselingSessionResponse(BaseModel):
    """心理咨询会话响应"""
    id: int
    session_id: str
    therapist_id: Optional[str] = None
    client_name: Optional[str] = None
    client_gender: Optional[str] = None
    client_age: Optional[int] = None
    title: Optional[str] = None
    description: Optional[str] = None
    session_date: datetime
    duration_minutes: Optional[int] = None
    created_at: datetime
    updated_at: datetime
    created_by: Optional[str] = None
    analysis: Optional["CounselingAnalysisResponse"] = None
    recordings: Optional[List["CounselingRecordingResponse"]] = None

    class Config:
        orm_mode = True


class CounselingDialogueResponse(BaseModel):
    """心理咨询对话响应"""
    id: int
    session_id: int
    dialogue_text: str
    created_at: datetime
    updated_at: datetime

    class Config:
        orm_mode = True


class CounselingRecordingResponse(BaseModel):
    """心理咨询录音响应"""
    id: int
    session_id: int
    file_path: str
    duration_seconds: Optional[int] = None
    status: RecordingStatus
    created_at: datetime
    updated_at: datetime

    class Config:
        orm_mode = True


class CounselingAnalysisResponse(BaseModel):
    """心理咨询分析响应"""
    id: int
    session_id: int
    status: AnalysisStatus
    risk_level: Optional[RiskLevel] = None
    analysis_results: Optional[Dict[str, Any]] = None
    report_content: Optional[str] = None
    created_at: datetime
    updated_at: datetime

    class Config:
        orm_mode = True


class AnalysisRequest(BaseModel):
    """分析请求"""
    session_id: int
    additional_info: Optional[Dict[str, Any]] = None
    force_reanalyze: bool = False


class AnalysisResponse(BaseModel):
    """分析响应"""
    success: bool = True
    session_id: int
    status: AnalysisStatus
    message: Optional[str] = None
    analysis_id: Optional[int] = None


class AnalysisResultResponse(BaseModel):
    """分析结果响应"""
    success: bool = True
    session_id: int
    analysis_id: int
    status: AnalysisStatus
    risk_level: Optional[RiskLevel] = None
    report_content: Optional[str] = None
    analysis_results: Optional[Dict[str, Any]] = None


# 更新前向引用
CounselingSessionResponse.model_rebuild()
