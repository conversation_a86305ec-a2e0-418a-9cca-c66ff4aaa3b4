<template>
  <view class="counseling-page">
    <!-- 页面标题和操作栏 -->
    <view class="page-header">
      <view class="header-content">
        <view class="title-section">
          <text class="page-title">专业咨询记录管理</text>
        </view>

        <view class="action-section">
          <view class="filter-group">
            <picker
              :value="roleIndex"
              :range="roleOptions"
              @change="handleRoleChange"
              class="role-picker"
            >
              <view class="picker-display">
                <text class="picker-text">{{ roleOptions[roleIndex] }}</text>
                <text class="picker-arrow">▼</text>
              </view>
            </picker>
          </view>

          <view class="button-group">
            <button class="action-btn refresh-btn" @click="loadSessions">
              <text class="btn-icon">⟳</text>
              <text class="btn-text">刷新</text>
            </button>

            <button class="action-btn info-btn" @click="showUsagePopup = true">
              <text class="btn-icon">ⓘ</text>
              <text class="btn-text">说明</text>
            </button>
          </view>
        </view>
      </view>
    </view>

    <view class="content-container">
      <!-- 使用说明弹窗 -->
      <uni-popup ref="usagePopup" type="center" :mask-click="true">
        <view class="popup-container">
          <view class="popup-header">
            <text class="popup-title">功能使用说明</text>
            <text class="popup-subtitle">心理咨询记录管理系统</text>
          </view>

          <view class="popup-content">
            <view class="feature-item">
              <text class="feature-icon">🎯</text>
              <text class="feature-text">专为心理咨询师设计的专业工具</text>
            </view>
            <view class="feature-item">
              <text class="feature-icon">📊</text>
              <text class="feature-text">智能分析咨询过程，生成详细报告</text>
            </view>
            <view class="feature-item">
              <text class="feature-icon">🔒</text>
              <text class="feature-text">安全记录，保护来访者隐私</text>
            </view>
            <view class="feature-item">
              <text class="feature-icon">📈</text>
              <text class="feature-text">提升咨询质量，优化服务流程</text>
            </view>
          </view>

          <view class="popup-footer">
            <text class="disclaimer">请遵守相关咨询服务协议及职业道德规范</text>
            <button class="popup-btn" @click="showUsagePopup = false">我知道了</button>
          </view>
        </view>
      </uni-popup>
      <view v-if="loading" class="loading-container">
        <view class="loading-spinner"></view>
        <text>加载中...</text>
      </view>

      <view v-else class="sessions-container">
        <view v-if="sessions.length === 0" class="empty-state">
          <text class="empty-icon">📋</text>
          <text class="empty-title">暂无咨询记录</text>
          <text class="empty-subtitle">点击右下角按钮创建新的咨询会话</text>
        </view>

        <view v-else class="sessions-list">
          <view
            v-for="session in sessions"
            :key="session.id"
            class="session-card"
            @click="viewSessionDetail(session.id)"
            @longpress="handleLongPress(session.id)"
          >
            <!-- 卡片头部 -->
            <view class="card-header">
              <view class="header-left">
                <text class="session-title">{{ session.title || '咨询会话' }}</text>
                <text class="session-id">ID: {{ formatSessionId(session.session_id || session.id) }}</text>
              </view>
              <view class="header-right">
                <view class="status-badge" :class="getStatusClass(session.status)">
                  {{ getStatusText(session.status) }}
                </view>
              </view>
            </view>

            <!-- 卡片内容 -->
            <view class="card-content">
              <!-- 基本信息行 -->
              <view class="info-row">
                <view class="info-item">
                  <text class="info-icon">👤</text>
                  <view class="info-text">
                    <text class="info-label">来访者</text>
                    <text class="info-value">{{ session.client_name || '匿名' }}</text>
                  </view>
                </view>
                <view class="info-item">
                  <text class="info-icon">📊</text>
                  <view class="info-text">
                    <text class="info-label">性别/年龄</text>
                    <text class="info-value">{{ getGenderText(session.client_gender) }}/{{ session.client_age || '未知' }}</text>
                  </view>
                </view>
              </view>

              <!-- 时间信息行 -->
              <view class="info-row">
                <view class="info-item">
                  <text class="info-icon">🕐</text>
                  <view class="info-text">
                    <text class="info-label">咨询时间</text>
                    <text class="info-value time-text">{{ formatDate(session.session_date || session.scheduled_time) }}</text>
                  </view>
                </view>
                <view class="info-item">
                  <text class="info-icon">⏱</text>
                  <view class="info-text">
                    <text class="info-label">时长</text>
                    <text class="info-value">{{ getSessionDuration(session) }}</text>
                  </view>
                </view>
              </view>

              <!-- 分析信息行 -->
              <view class="info-row" v-if="session.analysis && (session.analysis.risk_level || session.analysis.status)">
                <view class="info-item" v-if="session.analysis.risk_level">
                  <text class="info-icon">⚠️</text>
                  <view class="info-text">
                    <text class="info-label">风险等级</text>
                    <text class="info-value risk-badge" :class="getRiskLevelClass(session.analysis.risk_level)">
                      {{ getRiskLevelText(session.analysis.risk_level) }}
                    </text>
                  </view>
                </view>
                <view class="info-item" v-if="session.analysis.status">
                  <text class="info-icon">🔍</text>
                  <view class="info-text">
                    <text class="info-label">分析状态</text>
                    <text class="info-value analysis-badge" :class="getAnalysisStatusClass(session.analysis.status)">
                      {{ getAnalysisStatusText(session.analysis.status) }}
                    </text>
                  </view>
                </view>
              </view>

              <!-- 描述信息 -->
              <view class="description-row" v-if="session.description && session.description !== session.topic">
                <text class="description-icon">📝</text>
                <text class="description-text">{{ session.description }}</text>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 悬浮新建按钮 -->
    <view class="fab-container">
      <button class="fab-button" @click="startNewSession">
        <text class="fab-icon">+</text>
        <text class="fab-text">新建咨询</text>
      </button>
    </view>
  </view>
</template>

<script>
import counselingService from '../../services/counselingService';
import uniPopup from '../../components/uni-popup/uni-popup.vue'; // 引入 uni-popup 组件

export default {
  components: {
    uniPopup
  },
  data() {
    return {
      sessions: [],
      loading: true,
      roleOptions: ['全部', '咨询师', '来访者'],
      roleIndex: 0,
      roleValues: ['all', 'therapist', 'client'],
      showUsagePopup: false // 控制使用说明弹窗显示/隐藏
    }
  },
  onLoad() {
    this.loadSessions()
  },
  watch: {
    showUsagePopup(newVal) {
      if (newVal) {
        this.$refs.usagePopup.open();
      } else {
        this.$refs.usagePopup.close();
      }
    }
  },

  // 页面显示时刷新数据
  onShow() {
    // 如果页面已经加载过，则刷新数据
    if (this.sessions && this.sessions.length > 0) {
      console.log('页面重新显示，刷新数据');
      this.loadSessions();
    }
  },
  methods: {
    async loadSessions() {
      try {
        this.loading = true

        // 获取当前选择的角色
        const role = this.roleValues[this.roleIndex]
        console.log(`加载咨询会话列表: role=${role}`)

        // 调用咨询服务
        const result = await counselingService.getSessions(role)

        if (result.success) {
          console.log('获取咨询会话列表成功:', result.data)
          this.sessions = result.data || []

          // 调试：检查会话数据结构
          if (this.sessions.length > 0) {
            console.log('第一个会话数据结构:', JSON.stringify(this.sessions[0], null, 2))
            console.log('会话数量:', this.sessions.length)
            console.log('有analysis对象的会话数量:', this.sessions.filter(s => s.analysis).length)

            // 检查每个会话的analysis对象
            this.sessions.forEach((session, index) => {
              if (session.analysis) {
                console.log(`会话 ${index} (ID: ${session.id}) 的analysis对象:`,
                  JSON.stringify(session.analysis, null, 2))
              }
            })


          }

          // 检查分析状态和客户信息
          this.sessions.forEach(session => {
            // 记录分析状态
            if (session.analysis) {
              console.log(`会话 ID: ${session.id}, 分析状态: ${session.analysis.status}`)

              // 确保风险等级存在
              if (session.analysis.status === 'completed' && !session.analysis.risk_level) {
                console.log(`会话 ID: ${session.id} 分析已完成但没有风险等级，设置默认风险等级: low`)
                session.analysis.risk_level = 'low'
              }
            }

            // 检查客户信息是否完整
            if (!session.client_name || !session.client_gender || !session.client_age) {
              console.warn(`会话 ID: ${session.id} 的客户信息不完整`)
            }
          })

          // 检查返回的数据是否包含分析信息
          this.sessions.forEach((session) => {
            if (session.analysis) {
              console.log(`会话 ${session.id} 包含分析数据:`, {
                status: session.analysis.status,
                risk_level: session.analysis.risk_level
              })
            } else {
              console.log(`会话 ${session.id} 没有分析数据`)
            }
          })

          // 如果没有数据，不显示提示
          // 已移除"暂无咨询记录"的提示
        } else {
          console.error('获取咨询会话列表失败:', result.error)

          // 显示详细错误信息
          uni.showModal({
            title: '加载失败',
            content: result.error || '无法获取咨询记录',
            showCancel: false
          })

          // 设置为空数组
          this.sessions = []
        }
      } catch (error) {
        console.error('加载咨询记录失败:', error)

        // 显示详细错误信息
        uni.showModal({
          title: '加载失败',
          content: error.message || '无法获取咨询记录',
          showCancel: false
        })

        // 设置为空数组
        this.sessions = []
      } finally {
        this.loading = false
      }
    },

    // 处理角色选择变化
    handleRoleChange(e) {
      this.roleIndex = e.detail.value
      this.loadSessions()
    },

    formatDate(date) {
      if (!date) return '未设置'

      const d = new Date(date)
      const year = d.getFullYear()
      const month = (d.getMonth() + 1).toString().padStart(2, '0')
      const day = d.getDate().toString().padStart(2, '0')
      const hours = d.getHours().toString().padStart(2, '0')
      const minutes = d.getMinutes().toString().padStart(2, '0')

      return `${year}-${month}-${day} ${hours}:${minutes}`
    },

    getStatusText(status) {
      const statusMap = {
        'scheduled': '已创建',
        'in_progress': '进行中',
        'completed': '已完成',
        'cancelled': '已取消'
      }

      // 如果状态不在预定义的映射中，返回空字符串
      return statusMap[status] || ''
    },

    getStatusClass(status) {
      const classMap = {
        'scheduled': 'status-scheduled',
        'in_progress': 'status-in-progress',
        'completed': 'status-completed',
        'cancelled': 'status-cancelled'
      }

      return classMap[status] || ''
    },

    getSessionTypeText(type) {
      const typeMap = {
        'video': '视频咨询',
        'voice': '语音咨询',
        'text': '文字咨询',
        'in_person': '面对面咨询'
      }

      return typeMap[type] || '未知方式'
    },

    getAnalysisStatusText(status) {
      if (status === null || status === undefined) return ''; // Handle null/undefined as empty string
      const map = {
        'pending': '等待分析',
        'processing': '分析中',
        'completed': '分析完成',
        'failed': '分析失败',
        'user_cancelled': '已取消'
      };

      if (map[status]) return map[status];

      if (typeof status === 'string') {
        if (status.includes('fail') || status.includes('error')) return '分析失败';
        if (status.includes('process') || status.includes('running')) return '分析中';
        if (status.includes('complete') || status.includes('done') || status.includes('success')) return '分析完成';
        if (status.includes('wait') || status.includes('pending')) return '等待分析';
      }
      return ''; // Default fallback to empty string
    },

    getAnalysisStatusClass(status) {
      const classMap = {
        'not_started': 'status-not-started',
        'pending': 'status-pending',
        'processing': 'status-processing',
        'completed': 'status-completed',
        'failed': 'status-failed'
      }

      return classMap[status] || ''
    },

    // 获取性别文本
    getGenderText(gender) {
      if (!gender) return '未知'

      const genderMap = {
        'male': '男',
        'female': '女',
        'other': '其他'
      }

      return genderMap[gender.toLowerCase()] || gender
    },

    // 获取风险等级文本
    getRiskLevelText(level) {
      if (!level) return '未评估'

      // 将风险等级转换为小写进行比较
      const normalizedLevel = typeof level === 'string' ? level.toLowerCase() : '';

      const levelMap = {
        'low': '低风险',
        'medium': '中等风险',
        'high': '高风险',
        'critical': '严重风险'
      }

      return levelMap[normalizedLevel] || level
    },

    // 获取风险等级样式类
    getRiskLevelClass(level) {
      if (!level) return 'risk-low' // 默认使用低风险样式

      // 将风险等级转换为小写进行比较
      const normalizedLevel = typeof level === 'string' ? level.toLowerCase() : '';

      const classMap = {
        'low': 'risk-low',
        'medium': 'risk-medium',
        'high': 'risk-high',
        'critical': 'risk-critical'
      }

      return classMap[normalizedLevel] || 'risk-low'
    },

    // 格式化会话编号，使其显示为8位数字
    formatSessionId(id) {
      if (!id) return '';

      // 如果是cs_前缀的UUID格式，转换为8位数字
      if (typeof id === 'string' && id.startsWith('cs_')) {
        // 取UUID的前8位十六进制，转换为数字
        const hexPart = id.substring(3, 11);
        // 将十六进制转换为十进制数字
        const numericId = parseInt(hexPart, 16);
        // 确保是8位数字，不足前面补0
        return numericId.toString().padStart(8, '0');
      }

      // 如果是普通UUID格式，只取前8位
      if (typeof id === 'string' && id.includes('-')) {
        return id.split('-')[0];
      }

      // 如果是数字，确保是8位，不足前面补0
      if (typeof id === 'number' || !isNaN(parseInt(id))) {
        return parseInt(id).toString().padStart(8, '0');
      }

      // 其他情况直接返回
      return id.toString();
    },

    // 获取会话时长
    getSessionDuration(session) {
      // 如果有录音且有时长，优先显示录音时长
      if (session.recordings && Array.isArray(session.recordings) &&
          session.recordings.length > 0 && session.recordings[0].duration_seconds) {

        const totalSeconds = session.recordings[0].duration_seconds;
        const minutes = Math.floor(totalSeconds / 60);
        const seconds = Math.floor(totalSeconds % 60);

        return `${minutes}:${seconds.toString().padStart(2, '0')} (实际时长)`;
      }

      // 否则显示预计时长
      return session.duration_minutes ? `${session.duration_minutes} 分钟` : '未设置';
    },



    startNewSession() {
      uni.navigateTo({
        url: '/pages/counseling/create'
      })
    },

    viewSessionDetail(sessionId) {
      uni.navigateTo({
        url: `/pages/counseling/detail?id=${sessionId}`
      })
    },

    joinSession(sessionId) {
      uni.navigateTo({
        url: `/pages/counseling/detail?id=${sessionId}&action=join`
      })
    },







    handleLongPress(sessionId) {
      // 震动反馈
      uni.vibrateShort({
        success: function() {
          console.log('震动成功');
        }
      });

      // 显示操作菜单
      uni.showActionSheet({
        itemList: ['删除咨询'],
        itemColor: '#f5222d',
        success: (res) => {
          if (res.tapIndex === 0) {
            // 用户点击了"删除咨询"
            this.confirmDeleteSession(sessionId);
          }
        }
      });
    },

    confirmDeleteSession(sessionId) {
      uni.showModal({
        title: '删除会话',
        content: '确定要删除这个咨询会话吗？删除后无法恢复。',
        confirmText: '删除',
        confirmColor: '#f5222d',
        cancelText: '取消',
        success: (res) => {
          if (res.confirm) {
            this.deleteSession(sessionId)
          }
        }
      })
    },

    async deleteSession(sessionId) {
      try {
        uni.showLoading({
          title: '删除中...'
        })

        const result = await counselingService.deleteSession(sessionId)

        uni.hideLoading()

        if (result.success) {
          uni.showToast({
            title: '已删除会话',
            icon: 'success'
          })

          // 从列表中移除该会话
          const index = this.sessions.findIndex(s => s.id === sessionId)
          if (index !== -1) {
            this.sessions.splice(index, 1)
          }
        } else {
          uni.showModal({
            title: '删除失败',
            content: result.error || '删除会话失败，请稍后重试',
            showCancel: false
          })
        }
      } catch (error) {
        uni.hideLoading()
        console.error('删除会话失败:', error)
        uni.showModal({
          title: '删除失败',
          content: '删除会话失败，请稍后重试',
          showCancel: false
        })
      }
    }
  }
}
</script>

<style scoped>
/* ===== 全局样式 ===== */
.counseling-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', sans-serif;
}

/* ===== 页面头部 ===== */
.page-header {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  padding: 16px 16px 12px;
  position: sticky;
  top: 0;
  z-index: 100;
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
}

.title-section {
  text-align: center;
  margin-bottom: 12px;
}

.page-title {
  font-size: 22px;
  font-weight: 600;
  color: #2c3e50;
  display: block;
}

.action-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 12px;
}

/* 筛选器组 */
.filter-group {
  flex: 1;
}

.role-picker {
  width: 100%;
}

.picker-display {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  padding: 6px 10px;
  min-width: 80px;
}

.picker-text {
  font-size: 12px;
  color: #495057;
  font-weight: 500;
}

.picker-arrow {
  font-size: 10px;
  color: #6c757d;
  margin-left: 6px;
}

/* 按钮组 */
.button-group {
  display: flex;
  gap: 6px;
}

.action-btn {
  display: flex;
  align-items: center;
  gap: 3px;
  padding: 6px 10px;
  border-radius: 6px;
  border: none;
  font-size: 11px;
  font-weight: 500;
  transition: all 0.2s ease;
}

.refresh-btn {
  background: #e3f2fd;
  color: #1976d2;
}

.refresh-btn:active {
  background: #bbdefb;
}

.info-btn {
  background: #f3e5f5;
  color: #7b1fa2;
}

.info-btn:active {
  background: #e1bee7;
}

.btn-icon {
  font-size: 14px;
}

.btn-text {
  font-size: 11px;
}

/* ===== 内容区域 ===== */
.content-container {
  flex: 1;
  padding: 16px;
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
  box-sizing: border-box;
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  color: #6c757d;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid #e9ecef;
  border-top: 3px solid #007bff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 80px 20px;
  color: #6c757d;
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
  display: block;
}

.empty-title {
  font-size: 18px;
  font-weight: 600;
  color: #495057;
  margin-bottom: 8px;
  display: block;
}

.empty-subtitle {
  font-size: 14px;
  color: #6c757d;
  display: block;
}

/* ===== 会话列表 ===== */
.sessions-container {
  width: 100%;
}

.sessions-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

/* 会话卡片 */
.session-card {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  border: 1px solid rgba(0, 0, 0, 0.05);
  overflow: hidden;
  transition: all 0.3s ease;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
}

.session-card:active {
  transform: translateY(1px);
  box-shadow: 0 1px 8px rgba(0, 0, 0, 0.12);
}

/* 卡片头部 */
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 16px 16px 12px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.header-left {
  flex: 1;
}

.session-title {
  font-size: 16px;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 4px;
  display: block;
}

.session-id {
  font-size: 12px;
  color: #7f8c8d;
  font-family: 'SF Mono', Monaco, 'Cascadia Code', monospace;
  display: block;
}

.header-right {
  margin-left: 12px;
}

.status-badge {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
  text-align: center;
  min-width: 60px;
}

/* 状态样式 */
.status-scheduled {
  background: #e3f2fd;
  color: #1976d2;
}

.status-in-progress {
  background: #fff3e0;
  color: #f57c00;
}

.status-completed {
  background: #e8f5e8;
  color: #2e7d32;
}

.status-cancelled {
  background: #ffebee;
  color: #d32f2f;
}

/* 卡片内容 */
.card-content {
  padding: 16px;
}

.info-row {
  display: flex;
  gap: 16px;
  margin-bottom: 12px;
}

.info-row:last-child {
  margin-bottom: 0;
}

.info-item {
  flex: 1;
  display: flex;
  align-items: flex-start;
  gap: 8px;
  min-width: 0;
}

.info-icon {
  font-size: 16px;
  margin-top: 2px;
  flex-shrink: 0;
}

.info-text {
  flex: 1;
  min-width: 0;
}

.info-label {
  font-size: 12px;
  color: #6c757d;
  margin-bottom: 2px;
  display: block;
}

.info-value {
  font-size: 14px;
  color: #2c3e50;
  font-weight: 500;
  display: block;
  word-break: break-all;
}

.time-text {
  font-family: 'SF Mono', Monaco, 'Cascadia Code', monospace;
  font-size: 13px;
}

/* 风险等级和分析状态徽章 */
.risk-badge, .analysis-badge {
  padding: 2px 6px;
  border-radius: 8px;
  font-size: 12px;
  font-weight: 500;
}

.risk-low {
  background: #e8f5e8;
  color: #2e7d32;
}

.risk-medium {
  background: #fff3e0;
  color: #f57c00;
}

.risk-high {
  background: #ffebee;
  color: #d32f2f;
}

.risk-critical {
  background: #fce4ec;
  color: #c2185b;
}

.status-pending {
  background: #f3e5f5;
  color: #7b1fa2;
}

.status-processing {
  background: #e3f2fd;
  color: #1976d2;
}

.status-completed {
  background: #e8f5e8;
  color: #2e7d32;
}

.status-failed {
  background: #ffebee;
  color: #d32f2f;
}

/* 描述行 */
.description-row {
  display: flex;
  align-items: flex-start;
  gap: 8px;
  margin-top: 12px;
  padding-top: 12px;
  border-top: 1px solid rgba(0, 0, 0, 0.05);
}

.description-icon {
  font-size: 14px;
  margin-top: 2px;
  flex-shrink: 0;
  color: #6c757d;
}

.description-text {
  flex: 1;
  font-size: 13px;
  color: #6c757d;
  line-height: 1.4;
}

/* ===== 弹窗样式 ===== */
.popup-container {
  background: white;
  border-radius: 20px;
  padding: 24px;
  max-width: 360px;
  width: 90vw;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.15);
}

.popup-header {
  text-align: center;
  margin-bottom: 20px;
}

.popup-title {
  font-size: 20px;
  font-weight: 700;
  color: #2c3e50;
  margin-bottom: 4px;
  display: block;
}

.popup-subtitle {
  font-size: 14px;
  color: #7f8c8d;
  display: block;
}

.popup-content {
  margin-bottom: 24px;
}

.feature-item {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 16px;
}

.feature-item:last-child {
  margin-bottom: 0;
}

.feature-icon {
  font-size: 20px;
  flex-shrink: 0;
}

.feature-text {
  flex: 1;
  font-size: 14px;
  color: #495057;
  line-height: 1.4;
}

.popup-footer {
  text-align: center;
}

.disclaimer {
  font-size: 12px;
  color: #6c757d;
  margin-bottom: 16px;
  display: block;
  font-style: italic;
}

.popup-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 12px;
  padding: 12px 24px;
  font-size: 14px;
  font-weight: 600;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
  transition: all 0.3s ease;
}

.popup-btn:active {
  transform: translateY(1px);
  box-shadow: 0 2px 10px rgba(102, 126, 234, 0.4);
}

/* ===== 悬浮按钮 ===== */
.fab-container {
  position: fixed;
  bottom: 30px;
  right: 20px;
  z-index: 1000;
}

.fab-button {
  display: flex;
  align-items: center;
  gap: 6px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 20px;
  padding: 10px 16px;
  font-size: 13px;
  font-weight: 600;
  box-shadow: 0 4px 16px rgba(102, 126, 234, 0.3);
  transition: all 0.3s ease;
}

.fab-button:active {
  transform: translateY(1px);
  box-shadow: 0 2px 12px rgba(102, 126, 234, 0.3);
}

.fab-icon {
  font-size: 16px;
  font-weight: 300;
}

.fab-text {
  font-size: 13px;
  font-weight: 600;
}
</style>